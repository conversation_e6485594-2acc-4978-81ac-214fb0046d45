/**
 * Comprehensive Integration Tests for Lighthouse AI Workspace System
 * Tests UI/UX improvements, component loading, accessibility, and functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { act } from 'react-dom/test-utils';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock modules for testing
vi.mock('~/components/lighthouse/shared/store/lighthouse-store', () => ({
  useLighthouseStore: vi.fn(() => ({
    navigation: { currentModule: 'dashboard' },
    currentProject: {
      id: 'test-project',
      name: 'Test Project',
      intelligence: {
        learningLevel: 75,
        domainExpertise: {
          concepts: [
            { id: '1', name: 'React', confidence: 0.9 },
            { id: '2', name: 'TypeScript', confidence: 0.8 }
          ],
          vocabulary: [
            { term: 'component', frequency: 15 },
            { term: 'state', frequency: 12 }
          ],
          relatedDomains: ['Frontend', 'Development']
        }
      }
    },
    knowledgeCollections: [],
    knowledgeGraph: { edges: [] },
    learningEvents: [],
    insights: [],
    projectContext: { projectId: 'test-project' },
    intelligenceContext: null,
    setCurrentModule: vi.fn(),
    initializeSession: vi.fn(),
    updateIntelligenceContext: vi.fn()
  }))
}));

// Component imports
import { LighthouseMain } from '~/components/lighthouse/core/LighthouseMain';
import { KnowledgeHub } from '~/components/lighthouse/modules/knowledge/KnowledgeHub';
import { Dashboard } from '~/components/lighthouse/modules/dashboard/Dashboard';
import { NavigationSystem } from '~/components/lighthouse/core/NavigationSystem';
import { MetricCard } from '~/components/ui/enhanced-card';
import { LoadingOverlay } from '~/components/ui/loading-states';
import { useBreakpoint } from '~/components/ui/responsive-layout';

// Test wrapper with required providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="min-h-screen bg-background">
      {children}
    </div>
  );
};

describe('Lighthouse Integration Tests', () => {
  const user = userEvent.setup();
  
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock window.innerWidth for responsive tests
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
    
    // Mock ResizeObserver
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Core System Functionality', () => {
    it('should render LighthouseMain without errors', async () => {
      render(
        <TestWrapper>
          <LighthouseMain />
        </TestWrapper>
      );

      // Should show loading initially, then content
      expect(screen.getByText('Initializing Lighthouse workspace...')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing Lighthouse workspace...')).not.toBeInTheDocument();
      });
    });

    it('should handle module switching correctly', async () => {
      const mockSetCurrentModule = vi.fn();
      vi.mocked(require('~/components/lighthouse/shared/store/lighthouse-store').useLighthouseStore).mockReturnValue({
        navigation: { currentModule: 'dashboard' },
        currentProject: {
          id: 'test-project',
          name: 'Test Project',
          intelligence: {
            learningLevel: 75,
            domainExpertise: {
              concepts: [],
              vocabulary: [],
              relatedDomains: []
            }
          }
        },
        knowledgeCollections: [],
        knowledgeGraph: { edges: [] },
        learningEvents: [],
        insights: [],
        setCurrentModule: mockSetCurrentModule,
        initializeSession: vi.fn(),
        updateIntelligenceContext: vi.fn()
      });

      render(
        <TestWrapper>
          <LighthouseMain initialModule="knowledge" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockSetCurrentModule).toHaveBeenCalledWith('knowledge');
      });
    });

    it('should handle errors gracefully with error boundaries', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Component that throws an error
      const ErrorComponent = () => {
        throw new Error('Test error');
      };

      render(
        <TestWrapper>
          <ErrorComponent />
        </TestWrapper>
      );

      // Error boundary should catch and display error
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });

      consoleSpy.mockRestore();
    });
  });

  describe('UI Component Quality', () => {
    it('should render MetricCard with proper styling and interactions', async () => {
      render(
        <TestWrapper>
          <MetricCard
            title="Test Metric"
            value="42"
            icon={<span data-testid="test-icon">📊</span>}
            trend="up"
            change="+5%"
          />
        </TestWrapper>
      );

      const card = screen.getByText('Test Metric').closest('[role="article"]');
      expect(card).toBeInTheDocument();
      expect(screen.getByText('42')).toBeInTheDocument();
      expect(screen.getByText('+5%')).toBeInTheDocument();
      expect(screen.getByTestId('test-icon')).toBeInTheDocument();

      // Test hover interaction
      if (card) {
        await user.hover(card);
        expect(card).toHaveClass('hover:shadow-lg');
      }
    });

    it('should render LoadingOverlay with proper states', () => {
      const { rerender } = render(
        <TestWrapper>
          <LoadingOverlay loading={true} message="Loading test...">
            <div>Content</div>
          </LoadingOverlay>
        </TestWrapper>
      );

      expect(screen.getByText('Loading test...')).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();

      rerender(
        <TestWrapper>
          <LoadingOverlay loading={false}>
            <div>Content</div>
          </LoadingOverlay>
        </TestWrapper>
      );

      expect(screen.getByText('Content')).toBeInTheDocument();
      expect(screen.queryByText('Loading test...')).not.toBeInTheDocument();
    });

    it('should handle responsive breakpoints correctly', () => {
      // Mock mobile breakpoint
      Object.defineProperty(window, 'innerWidth', { value: 640 });
      
      const TestComponent = () => {
        const breakpoint = useBreakpoint();
        return <div data-testid="breakpoint">{breakpoint}</div>;
      };

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Trigger resize event
      act(() => {
        window.dispatchEvent(new Event('resize'));
      });

      expect(screen.getByTestId('breakpoint')).toHaveTextContent('mobile');
    });
  });

  describe('Knowledge Hub Module', () => {
    it('should render knowledge metrics correctly', async () => {
      render(
        <TestWrapper>
          <KnowledgeHub />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Knowledge Hub')).toBeInTheDocument();
        expect(screen.getByText('Concepts')).toBeInTheDocument();
        expect(screen.getByText('Connections')).toBeInTheDocument();
        expect(screen.getByText('Collections')).toBeInTheDocument();
      });
    });

    it('should handle search functionality', async () => {
      render(
        <TestWrapper>
          <KnowledgeHub />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText('Search knowledge base...');
      expect(searchInput).toBeInTheDocument();

      await user.type(searchInput, 'test query');
      expect(searchInput).toHaveValue('test query');
    });

    it('should switch between tabs correctly', async () => {
      render(
        <TestWrapper>
          <KnowledgeHub />
        </TestWrapper>
      );

      const collectionsTab = screen.getByRole('tab', { name: /collections/i });
      const graphTab = screen.getByRole('tab', { name: /graph/i });

      expect(collectionsTab).toHaveAttribute('data-state', 'active');

      await user.click(graphTab);
      expect(graphTab).toHaveAttribute('data-state', 'active');
    });

    it('should display concept confidence levels', async () => {
      render(
        <TestWrapper>
          <KnowledgeHub />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('React')).toBeInTheDocument();
        expect(screen.getByText('90%')).toBeInTheDocument();
        expect(screen.getByText('TypeScript')).toBeInTheDocument();
        expect(screen.getByText('80%')).toBeInTheDocument();
      });
    });
  });

  describe('Dashboard Module', () => {
    it('should render dashboard with project metrics', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Learning Progress')).toBeInTheDocument();
        expect(screen.getByText('75%')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility Compliance', () => {
    it('should meet accessibility standards', async () => {
      const { container } = render(
        <TestWrapper>
          <LighthouseMain />
        </TestWrapper>
      );

      await waitFor(async () => {
        const results = await axe(container);
        expect(results).toHaveNoViolations();
      });
    });

    it('should support keyboard navigation', async () => {
      render(
        <TestWrapper>
          <NavigationSystem />
        </TestWrapper>
      );

      // Tab through navigation items
      await user.tab();
      const focusedElement = document.activeElement;
      expect(focusedElement).toHaveAttribute('role', 'menuitem');
    });

    it('should have proper ARIA labels', () => {
      render(
        <TestWrapper>
          <MetricCard
            title="Test Metric"
            value="42"
            icon={<span>📊</span>}
          />
        </TestWrapper>
      );

      const card = screen.getByRole('article');
      expect(card).toHaveAttribute('aria-label');
    });
  });

  describe('Performance & Optimization', () => {
    it('should lazy load modules efficiently', async () => {
      const { container } = render(
        <TestWrapper>
          <LighthouseMain />
        </TestWrapper>
      );

      // Check for Suspense boundaries
      expect(container.querySelector('[data-testid="module-skeleton"]')).toBeInTheDocument();

      await waitFor(() => {
        expect(container.querySelector('[data-testid="module-skeleton"]')).not.toBeInTheDocument();
      });
    });

    it('should handle large datasets without performance issues', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i.toString(),
        name: `Concept ${i}`,
        confidence: Math.random()
      }));

      vi.mocked(require('~/components/lighthouse/shared/store/lighthouse-store').useLighthouseStore).mockReturnValue({
        navigation: { currentModule: 'knowledge' },
        currentProject: {
          id: 'test-project',
          name: 'Test Project',
          intelligence: {
            learningLevel: 75,
            domainExpertise: {
              concepts: largeDataset,
              vocabulary: [],
              relatedDomains: []
            }
          }
        },
        knowledgeCollections: [],
        knowledgeGraph: { edges: [] },
        learningEvents: [],
        insights: [],
        setCurrentModule: vi.fn(),
        initializeSession: vi.fn(),
        updateIntelligenceContext: vi.fn()
      });

      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <KnowledgeHub />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Knowledge Hub')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (< 1000ms)
      expect(renderTime).toBeLessThan(1000);
    });
  });

  describe('Error Handling & Edge Cases', () => {
    it('should handle missing project data gracefully', () => {
      vi.mocked(require('~/components/lighthouse/shared/store/lighthouse-store').useLighthouseStore).mockReturnValue({
        navigation: { currentModule: 'knowledge' },
        currentProject: null,
        knowledgeCollections: [],
        knowledgeGraph: { edges: [] },
        learningEvents: [],
        insights: [],
        setCurrentModule: vi.fn(),
        initializeSession: vi.fn(),
        updateIntelligenceContext: vi.fn()
      });

      render(
        <TestWrapper>
          <KnowledgeHub />
        </TestWrapper>
      );

      // Should render without crashing
      expect(document.body).toBeInTheDocument();
    });

    it('should handle network errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Simulate network error
      const mockFetch = vi.fn().mockRejectedValue(new Error('Network error'));
      global.fetch = mockFetch;

      render(
        <TestWrapper>
          <LighthouseMain />
        </TestWrapper>
      );

      // Should show error state or fallback
      await waitFor(() => {
        expect(screen.queryByText('Initializing Lighthouse workspace...')).not.toBeInTheDocument();
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Mobile Responsiveness', () => {
    it('should adapt layout for mobile screens', () => {
      Object.defineProperty(window, 'innerWidth', { value: 640 });
      
      render(
        <TestWrapper>
          <KnowledgeHub />
        </TestWrapper>
      );

      // Mobile layout should show condensed navigation
      const mobileHeader = screen.getByText('Knowledge Hub');
      expect(mobileHeader).toBeInTheDocument();
    });

    it('should handle touch interactions', async () => {
      Object.defineProperty(window, 'innerWidth', { value: 640 });
      
      render(
        <TestWrapper>
          <MetricCard
            title="Touch Test"
            value="42"
            icon={<span>📱</span>}
          />
        </TestWrapper>
      );

      const card = screen.getByText('Touch Test').closest('[role="article"]');
      
      if (card) {
        // Simulate touch events
        fireEvent.touchStart(card);
        fireEvent.touchEnd(card);
        
        expect(card).toBeInTheDocument();
      }
    });
  });
});