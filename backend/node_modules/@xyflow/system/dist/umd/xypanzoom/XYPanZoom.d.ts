import { type Viewport, PanZoomParams, PanZoomInstance } from '../types';
export type ZoomPanValues = {
    isZoomingOrPanning: boolean;
    usedRightMouseButton: boolean;
    prevViewport: Viewport;
    mouseButton: number;
    timerId: ReturnType<typeof setTimeout> | undefined;
    panScrollTimeout: ReturnType<typeof setTimeout> | undefined;
    isPanScrolling: boolean;
};
export declare function XYPanZoom({ domNode, minZoom, maxZoom, paneClickDistance, translateExtent, viewport, onPanZoom, onPanZoomStart, onPanZoomEnd, onDraggingChange, }: PanZoomParams): PanZoomInstance;
//# sourceMappingURL=XYPanZoom.d.ts.map