import { type KeyCode } from '@xyflow/system';
export type UseKeyPressOptions = {
    /**
     * Listen to key presses on a specific element.
     * @default document
     */
    target?: Window | Document | HTMLElement | ShadowRoot | null;
    /**
     * You can use this flag to prevent triggering the key press hook when an input field is focused.
     * @default true
     */
    actInsideInputWithModifier?: boolean;
    preventDefault?: boolean;
};
/**
 * This hook lets you listen for specific key codes and tells you whether they are
 * currently pressed or not.
 *
 * @public
 * @param options - Options
 *
 * @example
 * ```tsx
 *import { useKeyPress } from '@xyflow/react';
 *
 *export default function () {
 *  const spacePressed = useKeyPress('Space');
 *  const cmdAndSPressed = useKeyPress(['Meta+s', 'Strg+s']);
 *
 *  return (
 *    <div>
 *     {spacePressed && <p>Space pressed!</p>}
 *     {cmdAndSPressed && <p>Cmd + S pressed!</p>}
 *    </div>
 *  );
 *}
 *```
 */
export declare function useKeyPress(
/**
 * The key code (string or array of strings) specifies which key(s) should trigger
 * an action.
 *
 * A **string** can represent:
 * - A **single key**, e.g. `'a'`
 * - A **key combination**, using `'+'` to separate keys, e.g. `'a+d'`
 *
 * An  **array of strings** represents **multiple possible key inputs**. For example, `['a', 'd+s']`
 * means the user can press either the single key `'a'` or the combination of `'d'` and `'s'`.
 * @default null
 */
keyCode?: KeyCode | null, options?: UseKeyPressOptions): boolean;
//# sourceMappingURL=useKeyPress.d.ts.map