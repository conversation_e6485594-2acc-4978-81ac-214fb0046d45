import type { OnSelectionChangeFunc, Node, Edge } from '../../types';
type SelectionListenerProps<NodeType extends Node = Node, EdgeType extends Edge = Edge> = {
    onSelectionChange?: OnSelectionChangeFunc<NodeType, EdgeType>;
};
export declare function SelectionListener<NodeType extends Node = Node, EdgeType extends Edge = Edge>({ onSelectionChange, }: SelectionListenerProps<NodeType, EdgeType>): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=index.d.ts.map