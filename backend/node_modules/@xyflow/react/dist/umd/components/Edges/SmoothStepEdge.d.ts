import type { SmoothStepEdgeProps } from '../../types';
/**
 * Component that can be used inside a custom edge to render a smooth step edge.
 *
 * @public
 * @example
 *
 * ```tsx
 * import { SmoothStepEdge } from '@xyflow/react';
 *
 * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {
 *   return (
 *     <SmoothStepEdge
 *       sourceX={sourceX}
 *       sourceY={sourceY}
 *       targetX={targetX}
 *       targetY={targetY}
 *       sourcePosition={sourcePosition}
 *       targetPosition={targetPosition}
 *     />
 *   );
 * }
 * ```
 */
declare const SmoothStepEdge: import("react").MemoExoticComponent<({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition, targetPosition, markerEnd, markerStart, pathOptions, interactionWidth, }: SmoothStepEdgeProps) => import("react/jsx-runtime").JSX.Element>;
/**
 * @internal
 */
declare const SmoothStepEdgeInternal: import("react").MemoExoticComponent<({ id, sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition, targetPosition, markerEnd, markerStart, pathOptions, interactionWidth, }: SmoothStepEdgeProps) => import("react/jsx-runtime").JSX.Element>;
export { SmoothStepEdge, SmoothStepEdgeInternal };
//# sourceMappingURL=SmoothStepEdge.d.ts.map