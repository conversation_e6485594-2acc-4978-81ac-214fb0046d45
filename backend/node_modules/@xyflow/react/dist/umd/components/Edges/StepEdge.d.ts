import type { StepEdgeProps } from '../../types';
/**
 * Component that can be used inside a custom edge to render a step edge.
 *
 * @public
 * @example
 *
 * ```tsx
 * import { StepEdge } from '@xyflow/react';
 *
 * function CustomEdge({ sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition }) {
 *   return (
 *     <StepEdge
 *       sourceX={sourceX}
 *       sourceY={sourceY}
 *       targetX={targetX}
 *       targetY={targetY}
 *       sourcePosition={sourcePosition}
 *       targetPosition={targetPosition}
 *     />
 *   );
 * }
 * ```
 */
declare const StepEdge: import("react").MemoExoticComponent<({ id, ...props }: StepEdgeProps) => import("react/jsx-runtime").JSX.Element>;
/**
 * @internal
 */
declare const StepEdgeInternal: import("react").MemoExoticComponent<({ id, ...props }: StepEdgeProps) => import("react/jsx-runtime").JSX.Element>;
export { StepEdge, StepEdgeInternal };
//# sourceMappingURL=StepEdge.d.ts.map