import type { EdgeTextProps } from '../../types';
declare function EdgeTextComponent({ x, y, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, children, className, ...rest }: EdgeTextProps): import("react/jsx-runtime").JSX.Element | null;
declare namespace EdgeTextComponent {
    var displayName: string;
}
/**
 * You can use the `<EdgeText />` component as a helper component to display text
 * within your custom edges.
 *
 * @public
 *
 * @example
 * ```jsx
 * import { EdgeText } from '@xyflow/react';
 *
 * export function CustomEdgeLabel({ label }) {
 *   return (
 *     <EdgeText
 *       x={100}
 *       y={100}
 *       label={label}
 *       labelStyle={{ fill: 'white' }}
 *       labelShowBg
 *       labelBgStyle={{ fill: 'red' }}
 *       labelBgPadding={[2, 4]}
 *       labelBgBorderRadius={2}
 *     />
 *   );
 * }
 *```
 */
export declare const EdgeText: import("react").MemoExoticComponent<typeof EdgeTextComponent>;
export {};
//# sourceMappingURL=EdgeText.d.ts.map