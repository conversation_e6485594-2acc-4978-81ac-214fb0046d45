import { GraphViewProps } from '../GraphView';
import type { Node } from '../../types';
export type NodeRendererProps<NodeType extends Node> = Pick<GraphViewProps<NodeType>, 'onNodeClick' | 'onNodeDoubleClick' | 'onNodeMouseEnter' | 'onNodeMouseMove' | 'onNodeMouseLeave' | 'onNodeContextMenu' | 'onlyRenderVisibleElements' | 'noPanClassName' | 'noDragClassName' | 'rfId' | 'disableKeyboardA11y' | 'nodeExtent' | 'nodeTypes' | 'nodeClickDistance'>;
declare function NodeRendererComponent<NodeType extends Node>(props: NodeRendererProps<NodeType>): import("react/jsx-runtime").JSX.Element;
declare namespace NodeRendererComponent {
    var displayName: string;
}
export declare const NodeRenderer: typeof NodeRendererComponent;
export {};
//# sourceMappingURL=index.d.ts.map