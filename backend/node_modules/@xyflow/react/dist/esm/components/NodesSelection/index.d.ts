/**
 * The nodes selection rectangle gets displayed when a user
 * made a selection with on or several nodes
 */
import { type MouseEvent } from 'react';
import type { Node } from '../../types';
export type NodesSelectionProps<NodeType> = {
    onSelectionContextMenu?: (event: MouseEvent, nodes: NodeType[]) => void;
    noPanClassName?: string;
    disableKeyboardA11y: boolean;
};
export declare function NodesSelection<NodeType extends Node>({ onSelectionContextMenu, noPanClassName, disableKeyboardA11y, }: NodesSelectionProps<NodeType>): import("react/jsx-runtime").JSX.Element | null;
//# sourceMappingURL=index.d.ts.map